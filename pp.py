import requests
import re
import urllib.parse
import sys
import time

def paypal_login(email, password):
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Set headers to mimic a browser
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36",
        "Pragma": "no-cache",
        "Accept": "*/*"
    }
    
    # Initial GET request to obtain CSRF token and session ID
    login_url = "https://www.paypal.com/signin?intent=checkout&ctxId=ullndg5617253d9ee24859b87230a519f426d6&returnUri=%252Fwebapps%252Fhermes&state=%253Fflow%253D1-P%2526ulReturn%253Dtrue%2526token%253D68909346WP205534E%2526useraction%253Dcommit%2526rm%253D2%2526mfid%253D1493251164212_e335be39b9d1c%2526xclick_params%253DYnVzaW5lc3MlM0RhdGtpbnM3NiUyNTQwbmF2ZXIuY29tJTI2aXRlbV9uYW1lJTNEQ2xpcCUyNTIwRG93bmxvYWQlMjUyMC0lMjUyMCUyNTI4JTI1RUQlMjU5NSUyNTlDJTI1RUElMjVCOCUyNTgwJTI1MjlBbGwlMjUyMHRoYXQlMjUyMGNhdGZpZ2h0JTI1MjB2b2wuMyUyNTIwb2ZmaWNlJTI1MjBzdG9yeSUyNTIwcGFydC4zJTI1MjBQdW5pc2htZW50JTI1MjBjYXRmaWdodCUyNTIwJTI1MjhBbGwlMjUyMHRoYXQlMjUyMGNhdGZpZ2h0JTI1MjB2b2wuMyUyNTI5JTI2YW1vdW50JTNEOC4wMCUyNnJldHVybiUzRGh0dHAlMjUzQSUyNTJGJTI1MkZ3d3cuY2F0ZmlnaHQuY28ua3IlMjUyRnBheXBhbCUyNTJGc3VjY2Vzcy5waHAlMjUzRm9pZCUyNTNEMjAxNzA0MjcwODU5MjE3MDE4JTI2Y2FuY2VsX3JldHVybiUzRGh0dHAlMjUzQSUyNTJGJTI1MkZ3d3cuY2F0ZmlnaHQuY28ua3IlMjUyRnVwZGF0ZXMucGhwJTI2Y2hhcnNldCUzRHV0Zi04JTI2Y2J0JTNETXVzdCUyNTIwY2xpY2slMjUyMHRoaXMlMjUyMGZvciUyNTIwRG93bmxvYWQlMjUyMGNsaXAlMjUyMSUyNTIxJTI2bm9fc2hpcHBpbmclM0QxJTI2cm0lM0QyJTI2bm9fbm90ZSUzRDElMjZ3YV90eXBlJTNEQnV5Tm93JTI2Y291bnRlcnBhcnR5JTNEMTYxNjU1NDAzNzA0MDI2MjY5MA%25253D%25253D&flowId=68909346WP205534E&country.x=KR&locale.x=en_US"
    
    print("[*] Sending initial GET request...")
    response = session.get(login_url, headers=headers)
    
    if response.status_code != 200:
        print(f"[!] Failed to load login page. Status code: {response.status_code}")
        return {"status": "FAIL", "message": "Failed to load login page"}
    
    # Parse CSRF token
    csrf_token_match = re.search(r'"data-csrf-token="([^"]+)"', response.text)
    if not csrf_token_match:
        print("[!] Failed to extract CSRF token")
        return {"status": "FAIL", "message": "Failed to extract CSRF token"}
    
    csrf_token = csrf_token_match.group(1)
    csrf_token_encoded = urllib.parse.quote(csrf_token)
    
    # Parse session ID
    session_id_match = re.search(r'name="_sessionID" value="([^"]+)"', response.text)
    if not session_id_match:
        print("[!] Failed to extract session ID")
        return {"status": "FAIL", "message": "Failed to extract session ID"}
    
    session_id = session_id_match.group(1)
    
    # URL encode email
    email_encoded = urllib.parse.quote(email)
    
    # Prepare POST data
    post_data = (
        f"_csrf={csrf_token_encoded}&"
        f"_sessionID={session_id}&"
        f"locale.x=en_US&"
        f"processSignin=main&"
        f"fn_sync_data=%257B%2522SC_VERSION%2522%253A%25222.0.1%2522%252C%2522syncStatus%2522%253A%2522data%2522%252C%2522f%2522%253A%252268909346WP205534E%2522%252C%2522s%2522%253A%2522UL_CHECKOUT_INPUT_PASSWORD%2522%252C%2522chk%2522%253A%257B%2522ts%2522%253A1695057893170%252C%2522eteid%2522%253A%255B-1294079789%252C13188799918%252C-10949659039%252C8199468597%252C-1023571641%252C-3729849861%252Cnull%252Cnull%255D%252C%2522tts%2522%253A5680%257D%252C%2522dc%2522%253A%2522%257B%255C%2522screen%255C%2522%253A%257B%255C%2522colorDepth%255C%2522%253A24%252C%255C%2522pixelDepth%255C%2522%253A24%252C%255C%2522height%255C%2522%253A768%252C%255C%2522width%255C%2522%253A1612%252C%255C%2522availHeight%255C%2522%253A728%252C%255C%2522availWidth%255C%2522%253A1612%257D%252C%255C%2522ua%255C%2522%253A%255C%2522Mozilla%252F5.0%2520%28Windows%2520NT%252010.0%253B%2520Win64%253B%2520x64%29%2520AppleWebKit%252F537.36%2520%28KHTML%252C%2520like%2520Gecko%29%2520Chrome%252F116.0.0.0%2520Safari%252F537.36%255C%2522%257D%2522%252C%2522d%2522%253A%257B%2522ts1%2522%253A%2522Dk000%253A2729Uk000%253A2Uh%253A1033Uh%253A1479%2522%252C%2522rDT%2522%253A%252246987%252C46448%252C46907%253A16175%252C15454%252C17810%253A41909%252C41434%252C42078%253A52036%252C51537%252C55002%253A52630%252C51459%252C52302%253A11624%252C10777%252C11674%253A21908%252C20803%252C21902%253A42344%252C41524%252C41909%253A6508%252C5368%252C6729%253A31060%252C31256%252C37653%253A9093%252C12%2522%257D%257D&"
        f"intent=checkout&"
        f"ads-client-context=checkout&"
        f"flowId=68909346WP205534E&"
        f"ads-client-context-data=%7B%22context_id%22%3A%7B%22context_id%22%3A%2268909346WP205534E%22%2C%22channel%22%3A0%2C%22flow_type%22%3A%22checkout%22%7D%7D&"
        f"ctxId=ullndg5617253d9ee24859b87230a519f426d6&"
        f"isValidCtxId=true&"
        f"coBrand=kr&"
        f"signUpEndPoint=%2Fwebapps%2Fmpp%2Faccount-selection&"
        f"isSplitLoginVariant=true&"
        f"hideOtpLoginCredentials=true&"
        f"requestUrl=%2Fsignin%3Fintent%3Dcheckout%26ctxId%3Dullndg5617253d9ee24859b87230a519f426d6%26returnUri%3D%25252Fwebapps%25252Fhermes%26state%3D%25253Fflow%25253D1-P%252526ulReturn%25253Dtrue%252526token%25253D68909346WP205534E%252526useraction%25253Dcommit%252526rm%25253D2%252526mfid%25253D1493251164212_e335be39b9d1c%252526xclick_params%25253DYnVzaW5lc3MlM0RhdGtpbnM3NiUyNTQwbmF2ZXIuY29tJTI2aXRlbV9uYW1lJTNEQ2xpcCUyNTIwRG93bmxvYWQlMjUyMC0lMjUyMCUyNTI4JTI1RUQlMjU5NSUyNTlDJTI1RUElMjVCOCUyNTgwJTI1MjlBbGwlMjUyMHRoYXQlMjUyMGNhdGZpZ2h0JTI1MjB2b2wuMyUyNTIwb2ZmaWNlJTI1MjBzdG9yeSUyNTIwcGFydC4zJTI1MjBQdW5pc2htZW50JTI1MjBjYXRmaWdodCUyNTIwJTI1MjhBbGwlMjUyMHRoYXQlMjUyMGNhdGZpZ2h0JTI1MjB2b2wuMyUyNTI5JTI2YW1vdW50JTNEOC4wMCUyNnJldHVybiUzRGh0dHAlMjUzQSUyNTJGJTI1MkZ3d3cuY2F0ZmlnaHQuY28ua3IlMjUyRnBheXBhbCUyNTJGc3VjY2Vzcy5waHAlMjUzRm9pZCUyNTNEMjAxNzA0MjcwODU5MjE3MDE4JTI2Y2FuY2VsX3JldHVybiUzRGh0dHAlMjUzQSUyNTJGJTI1MkZ3d3cuY2F0ZmlnaHQuY28ua3IlMjUyRnVwZGF0ZXMucGhwJTI2Y2hhcnNldCUzRHV0Zi04JTI2Y2J0JTNETXVzdCUyNTIwY2xpY2slMjUyMHRoaXMlMjUyMGZvciUyNTIwRG93bmxvYWQlMjUyMGNsaXAlMjUyMSUyNTIxJTI2bm9fc2hpcHBpbmclM0QxJTI2cm0lM0QyJTI2bm9fbm90ZSUzRDElMjZ3YV90eXBlJTNEQnV5Tm93JTI2Y291bnRlcnBhcnR5JTNEMTYxNjU1NDAzNzA0MDI2MjY5MA%2525253D%2525253D%26flowId%3D68909346WP205534E%26country.x%3DKR%26locale.x%3Den_US&"
        f"forcePhonePasswordOptIn=&"
        f"returnUri=%252Fwebapps%252Fhermes&"
        f"state=%253Fflow%253D1-P%2526ulReturn%253Dtrue%2526token%253D68909346WP205534E%2526useraction%253Dcommit%2526rm%253D2%2526mfid%253D1493251164212_e335be39b9d1c%2526xclick_params%253DYnVzaW5lc3MlM0RhdGtpbnM3NiUyNTQwbmF2ZXIuY29tJTI2aXRlbV9uYW1lJTNEQ2xpcCUyNTIwRG93bmxvYWQlMjUyMC0lMjUyMCUyNTI4JTI1RUQlMjU5NSUyNTlDJTI1RUElMjVCOCUyNTgwJTI1MjlBbGwlMjUyMHRoYXQlMjUyMGNhdGZpZ2h0JTI1MjB2b2wuMyUyNTIwb2ZmaWNlJTI1MjBzdG9yeSUyNTIwcGFydC4zJTI1MjBQdW5pc2htZW50JTI1MjBjYXRmaWdodCUyNTIwJTI1MjhBbGwlMjUyMHRoYXQlMjUyMGNhdGZpZ2h0JTI1MjB2b2wuMyUyNTI5JTI2YW1vdW50JTNEOC4wMCUyNnJldHVybiUzRGh0dHAlMjUzQSUyNTJGJTI1MkZ3d3cuY2F0ZmlnaHQuY28ua3IlMjUyRnBheXBhbCUyNTJGc3VjY2Vzcy5waHAlMjUzRm9pZCUyNTNEMjAxNzA0MjcwODU5MjE3MDE4JTI2Y2FuY2VsX3JldHVybiUzRGh0dHAlMjUzQSUyNTJGJTI1MkZ3d3cuY2F0ZmlnaHQuY28ua3IlMjUyRnVwZGF0ZXMucGhwJTI2Y2hhcnNldCUzRHV0Zi04JTI2Y2J0JTNETXVzdCUyNTIwY2xpY2slMjUyMHRoaXMlMjUyMGZvciUyNTIwRG93bmxvYWQlMjUyMGNsaXAlMjUyMSUyNTIxJTI2bm9fc2hpcHBpbmclM0QxJTI2cm0lM0QyJTI2bm9fbm90ZSUzRDElMjZ3YV90eXBlJTNEQnV5Tm93JTI2Y291bnRlcnBhcnR5JTNEMTYxNjU1NDAzNzA0MDI2MjY5MA%25253D%25253D&"
        f"phoneCode=KR+%2B82&"
        f"login_email={email_encoded}&"
        f"captchaCode=&"
        f"initialSplitLoginContext=inputEmail&"
        f"isTpdOnboarded=&"
        f"login_password={urllib.parse.quote(password)}&"
        f"captcha=&"
        f"splitLoginContext=inputPassword"
    )
    
    # Add additional headers for POST request
    post_headers = headers.copy()
    post_headers.update({
        "Content-Type": "application/x-www-form-urlencoded",
        "Origin": "https://www.paypal.com",
        "Referer": login_url
    })
    
    print("[*] Sending login POST request...")
    login_response = session.post(login_url, data=post_data, headers=post_headers, allow_redirects=True)
    
    # Check for success or failure indicators
    if "LoginFailed" in login_response.text or "Some of your info isn't" in login_response.text:
        print("[!] Login failed: Invalid credentials")
        return {"status": "FAIL", "message": "Invalid credentials"}
    
    elif "adsRecaptchaSiteKey" in login_response.text or "captcha code" in login_response.text:
        print("[!] CAPTCHA detected")
        return {"status": "RETRY", "message": "CAPTCHA required"}
    
    elif "Quick security" in login_response.text or "https://www.paypal.com/webapps" in login_response.text:
        print("[+] Login successful!")
        
        # Try to extract phone number if available
        phone_match = re.search(r'data-value="([^"]+)">Mobile', login_response.text)
        phone = phone_match.group(1) if phone_match else "Not found"
        
        # Try to extract region
        region_match = re.search(r'country: ([^,]+),', login_response.text)
        region_code = region_match.group(1) if region_match else "Unknown"
        
        # Map region code to country name (simplified version)
        country_map = {
            "US": "United States",
            "GB": "United Kingdom",
            "CA": "Canada",
            "AU": "Australia",
            "DE": "Germany",
            "FR": "France",
            "KR": "Korea: Republic of",
            # Add more as needed
        }
        
        country = country_map.get(region_code, f"Unknown ({region_code})")
        
        return {
            "status": "SUCCESS",
            "message": "Login successful",
            "phone": phone,
            "country": country,
            "country_code": region_code
        }
    
    elif "is limited" in login_response.text:
        print("[!] Account is limited")
        return {"status": "CUSTOM", "message": "Account is limited"}
    
    elif "https://www.paypal.com/authflow/safe" in login_response.url or "https://www.paypal.com/twofactor/" in login_response.url:
        print("[!] Two-factor authentication required")
        return {"status": "2FACTOR", "message": "Two-factor authentication required"}
    
    else:
        print("[?] Unknown response, checking URL...")
        print(f"Current URL: {login_response.url}")
        
        if "authflow" in login_response.url:
            print("[+] Redirected to authentication flow, likely successful")
            return {"status": "SUCCESS", "message": "Login appears successful (redirected to auth flow)"}
        else:
            print("[!] Unrecognized response")
            return {"status": "UNKNOWN", "message": f"Unrecognized response, URL: {login_response.url}"}

def main():
    if len(sys.argv) != 3:
        print("Usage: python paypal_login.py <email> <password>")
        sys.exit(1)
    
    email = sys.argv[1]
    password = sys.argv[2]
    
    print(f"[*] Attempting to login with email: {email}")
    result = paypal_login(email, password)
    
    print("\n[*] Result:")
    for key, value in result.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    main()