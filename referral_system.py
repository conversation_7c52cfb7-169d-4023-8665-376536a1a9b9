"""
Referral system for the Apes OTP Bot.
This module handles referral tracking, link generation, and reward claiming.
"""

import logging
import datetime
import time
from pymongo import MongoClient
from dotenv import load_dotenv
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Cache for frequently accessed data
# Simple time-based cache for referral counts and lists
referral_count_cache = {}
referral_list_cache = {}
CACHE_TTL = 300  # 5 minutes in seconds

# Connect to MongoDB
load_dotenv()
uri = os.getenv("MONGODB_URI")
if not uri:
    logger.warning("MONGODB_URI environment variable not set. Using default value.")
    uri = "mongodb://localhost:27017/"

bot_username = os.getenv("botusername")
if not bot_username:
    logger.warning("botusername environment variable not set. Using default value.")
    bot_username = "trying_py_bot"

# Initialize MongoDB connection with error handling
try:
    client = MongoClient(uri, serverSelectionTimeoutMS=5000)  # 5 second timeout
    # Verify connection is working
    client.server_info()
    logger.info("Successfully connected to MongoDB in referral_system")
    db = client["otpbot"]
    referrals_collection = db["referrals"]
    users_collection = db["users"]
    keys_collection = db["keys"]
except Exception as e:
    logger.error(f"Failed to connect to MongoDB in referral_system: {str(e)}")
    logger.warning("Using fallback in-memory storage for referral system. Data will not persist!")

    # Create fallback in-memory storage
    class MemoryCollection:
        def __init__(self):
            self.data = []

        def find_one(self, query, sort=None):
            for item in self.data:
                match = True
                for key, value in query.items():
                    if key not in item or item[key] != value:
                        match = False
                        break
                if match:
                    return item
            return None

        def insert_one(self, document):
            if "_id" not in document:
                document["_id"] = str(datetime.datetime.now())
            self.data.append(document)
            return type('obj', (object,), {'inserted_id': document["_id"]})

        def update_one(self, query, update, upsert=False):
            item = self.find_one(query)
            if item:
                for key, value in update.get("$set", {}).items():
                    item[key] = value
            elif upsert:
                new_doc = {k: v for k, v in query.items()}
                for key, value in update.get("$set", {}).items():
                    new_doc[key] = value
                self.insert_one(new_doc)

        def update_many(self, query, update):
            for item in self.data:
                match = True
                for key, value in query.items():
                    if key not in item or item[key] != value:
                        match = False
                        break
                if match:
                    for key, value in update.get("$set", {}).items():
                        item[key] = value

        def count_documents(self, query):
            count = 0
            for item in self.data:
                match = True
                for key, value in query.items():
                    if key not in item or item[key] != value:
                        match = False
                        break
                if match:
                    count += 1
            return count

        def find(self, query, sort=None):
            results = []
            for item in self.data:
                match = True
                for key, value in query.items():
                    if key not in item or item[key] != value:
                        match = False
                        break
                if match:
                    results.append(item)

            # Simple implementation of sort
            if sort and results:
                field = sort[0][0]
                direction = sort[0][1]
                reverse = direction == -1
                results.sort(key=lambda x: x.get(field, 0), reverse=reverse)

            # Return a list-like object with a limit method
            class ResultSet:
                def __init__(self, results):
                    self.results = results

                def limit(self, limit):
                    return self.results[:limit]

            return ResultSet(results)

        def create_index(self, field, unique=False):
            logger.info(f"Creating index on {field} (unique={unique}) - no-op for memory storage")

    # Create in-memory collections
    db = {"name": "memory_db"}
    referrals_collection = MemoryCollection()
    users_collection = MemoryCollection()
    keys_collection = MemoryCollection()

def clear_cache_for_user(user_id):
    """
    Clear all cached data for a specific user.

    Args:
        user_id: The Telegram user ID
    """
    try:
        # Clear referral count cache
        count_cache_key = f"referral_count_{user_id}"
        if count_cache_key in referral_count_cache:
            del referral_count_cache[count_cache_key]
            logger.info(f"Cleared referral count cache for user {user_id}")

        # Clear all referral list caches for this user
        # We need to find all keys that match the pattern
        list_keys_to_delete = [k for k in referral_list_cache.keys() if k.startswith(f"referral_list_{user_id}_")]
        for key in list_keys_to_delete:
            del referral_list_cache[key]
            logger.info(f"Cleared referral list cache for user {user_id}")
    except Exception as e:
        logger.error(f"Error clearing cache for user {user_id}: {e}")

def cleanup_cache():
    """
    Clean up expired cache entries to prevent memory leaks.
    This should be called periodically.
    """
    try:
        current_time = time.time()

        # Clean up referral count cache
        count_keys_to_delete = []
        for key, data in referral_count_cache.items():
            if current_time - data['timestamp'] >= CACHE_TTL:
                count_keys_to_delete.append(key)

        for key in count_keys_to_delete:
            del referral_count_cache[key]

        # Clean up referral list cache
        list_keys_to_delete = []
        for key, data in referral_list_cache.items():
            if current_time - data['timestamp'] >= CACHE_TTL:
                list_keys_to_delete.append(key)

        for key in list_keys_to_delete:
            del referral_list_cache[key]

        if count_keys_to_delete or list_keys_to_delete:
            logger.info(f"Cleaned up {len(count_keys_to_delete)} count cache entries and {len(list_keys_to_delete)} list cache entries")
    except Exception as e:
        logger.error(f"Error cleaning up cache: {e}")

def init_referral_system():
    """Initialize the referral system by creating necessary indexes"""
    try:
        # Create index on referrer_id for faster lookups
        referrals_collection.create_index("referrer_id")
        # Create index on referred_id to ensure uniqueness
        referrals_collection.create_index("referred_id", unique=True)
        # Schedule cache cleanup
        logger.info("Referral system initialized")
    except Exception as e:
        logger.warning(f"Error creating payment session indexes: {str(e)}")
        logger.info("Referral system initialized with warnings")

def generate_referral_link(user_id):
    """
    Generate a referral link for a user.

    Args:
        user_id: The Telegram user ID

    Returns:
        str: The referral link
    """
    # Create a deep link using the bot's username and the user's ID as the start parameter
    return f"https://t.me/{bot_username}?start=ref_{user_id}"

def track_referral(referrer_id, referred_id, referred_username=None):
    """
    Track a new referral.

    Args:
        referrer_id: The ID of the user who referred
        referred_id: The ID of the user who was referred
        referred_username: The username of the referred user (optional)

    Returns:
        bool: True if the referral was successfully tracked, False otherwise
    """
    try:
        # Make sure IDs are integers
        if isinstance(referrer_id, str):
            referrer_id = int(referrer_id)
        if isinstance(referred_id, str):
            referred_id = int(referred_id)

        logger.info(f"Tracking referral: referrer_id={referrer_id} ({type(referrer_id)}), referred_id={referred_id} ({type(referred_id)}), username={referred_username}")

        # Check if the referred user already exists in the users collection
        existing_user = users_collection.find_one({"chat_id": referred_id})
        if existing_user:
            logger.info(f"User {referred_id} already exists in the database, not counting as a referral")
            return False

        # Check if this user has already been referred
        existing_referral = referrals_collection.find_one({"referred_id": referred_id})
        if existing_referral:
            logger.info(f"User {referred_id} has already been referred by user {existing_referral['referrer_id']}")
            return False

        # Add the referral to the database
        try:
            # Create the document to insert
            referral_doc = {
                "referrer_id": referrer_id,
                "referred_id": referred_id,
                "referred_username": referred_username,
                "timestamp": datetime.datetime.now(),
                "status": "active"  # Could be used later for additional status tracking
            }

            # Log the document we're about to insert
            logger.info(f"Inserting referral document: {referral_doc}")

            # Insert the document
            result = referrals_collection.insert_one(referral_doc)

            # Log the result
            logger.info(f"Referral inserted with ID: {result.inserted_id}")
            logger.info(f"User {referred_id} was successfully referred by user {referrer_id}")

            # Clear any cached data for this referrer
            clear_cache_for_user(referrer_id)

            return True
        except Exception as e:
            logger.error(f"Error inserting referral into database: {e}")
            return False
    except Exception as e:
        logger.error(f"Error in track_referral: {e}")
        return False

def get_referral_count(user_id):
    """
    Get the number of successful referrals for a user.
    Uses a time-based cache to improve performance.

    Args:
        user_id: The Telegram user ID

    Returns:
        int: The number of referrals
    """
    try:
        # Make sure user_id is an integer
        if isinstance(user_id, str):
            user_id = int(user_id)

        # Check if we have a cached value
        cache_key = f"referral_count_{user_id}"
        current_time = time.time()

        if cache_key in referral_count_cache:
            cached_data = referral_count_cache[cache_key]
            # If the cache is still valid, return the cached count
            if current_time - cached_data['timestamp'] < CACHE_TTL:
                logger.info(f"Using cached referral count for user {user_id}: {cached_data['count']}")
                return cached_data['count']

        # If no cache or expired, query the database
        logger.info(f"Getting referral count for user_id: {user_id}, type: {type(user_id)}")

        # Get the count
        count = referrals_collection.count_documents({"referrer_id": user_id})

        # Cache the result
        referral_count_cache[cache_key] = {
            'count': count,
            'timestamp': current_time
        }

        logger.info(f"Found {count} referrals for user {user_id}")
        return count
    except Exception as e:
        logger.error(f"Error getting referral count for user {user_id}: {e}")
        return 0

def get_referral_progress(user_id):
    """
    Get the referral progress for a user.

    Args:
        user_id: The Telegram user ID

    Returns:
        dict: A dictionary containing the referral count, target, and progress percentage
    """
    referral_count = get_referral_count(user_id)
    target = 20  # The target number of referrals needed for a reward
    progress_percentage = min(100, int((referral_count / target) * 100))

    return {
        "count": referral_count,
        "target": target,
        "percentage": progress_percentage,
        "can_claim": referral_count >= target
    }

def generate_progress_bar(percentage, length=10):
    """
    Generate a text-based progress bar.

    Args:
        percentage: The percentage of completion (0-100)
        length: The length of the progress bar in characters

    Returns:
        str: A text-based progress bar
    """
    filled_length = int(length * percentage / 100)
    bar = '█' * filled_length + '░' * (length - filled_length)
    return f"[{bar}] {percentage}%"

def can_claim_reward(user_id):
    """
    Check if a user can claim a referral reward.

    Args:
        user_id: The Telegram user ID

    Returns:
        bool: True if the user can claim a reward, False otherwise
    """
    # Check if the user has enough referrals
    referral_count = get_referral_count(user_id)
    if referral_count < 20:
        return False

    # Check if the user has already claimed a reward recently
    last_claim = referrals_collection.find_one(
        {"referrer_id": user_id, "reward_claimed": True},
        sort=[("reward_claimed_at", -1)]
    )

    if last_claim:
        # Check if it's been at least 20 days since the last claim
        last_claim_time = last_claim.get("reward_claimed_at")
        if last_claim_time:
            time_since_last_claim = datetime.datetime.now() - last_claim_time
            if time_since_last_claim.days < 20:
                return False

    return True

def claim_reward(user_id):
    """
    Claim a referral reward for a user.

    Args:
        user_id: The Telegram user ID

    Returns:
        dict: A dictionary containing the result of the claim
    """
    # Check if the user can claim a reward
    if not can_claim_reward(user_id):
        return {"success": False, "message": "You don't have enough referrals or you've claimed a reward recently."}

    # Generate a 1-day key
    prefix = "Ape"
    import random
    import string
    code = ["".join(random.choices(string.ascii_uppercase + string.digits, k=5)) for _ in range(4)]
    key = f"{prefix}-{code[0]}-{code[1]}-{code[2]}-{code[3]}"

    # Store the key in the database
    keys_collection.insert_one({
        "key": key,
        "Duration": "1Day",
        "used": False,
        "referral_reward": True,
        "referrer_id": user_id
    })

    # Mark the referrals as having been used for a reward
    referrals_collection.update_many(
        {"referrer_id": user_id, "reward_claimed": {"$ne": True}},
        {"$set": {"reward_claimed": True, "reward_claimed_at": datetime.datetime.now()}}
    )

    # Clear cache for this user since their referral status has changed
    clear_cache_for_user(user_id)

    return {
        "success": True,
        "message": "Congratulations! You've earned a 1-day subscription key.",
        "key": key
    }

def get_referral_list(user_id, limit=10):
    """
    Get a list of referrals for a user.
    Uses a time-based cache to improve performance.

    Args:
        user_id: The Telegram user ID
        limit: The maximum number of referrals to return

    Returns:
        list: A list of referral documents
    """
    try:
        # Make sure user_id is an integer
        if isinstance(user_id, str):
            user_id = int(user_id)

        # Check if we have a cached value
        cache_key = f"referral_list_{user_id}_{limit}"
        current_time = time.time()

        if cache_key in referral_list_cache:
            cached_data = referral_list_cache[cache_key]
            # If the cache is still valid, return the cached list
            if current_time - cached_data['timestamp'] < CACHE_TTL:
                logger.info(f"Using cached referral list for user {user_id}")
                return cached_data['referrals']

        # Log the query we're about to make
        logger.info(f"Getting referrals for user_id: {user_id}, type: {type(user_id)}")

        # Get referrals from the database
        referrals = referrals_collection.find(
            {"referrer_id": user_id},
            sort=[("timestamp", -1)]
        ).limit(limit)

        # Convert to list and log the count
        result = list(referrals)
        logger.info(f"Found {len(result)} referrals for user {user_id}")

        # Cache the result
        referral_list_cache[cache_key] = {
            'referrals': result,
            'timestamp': current_time
        }

        # Log the first few referrals for debugging
        if result:
            for i, ref in enumerate(result[:3]):  # Log up to 3 referrals
                logger.info(f"Referral {i+1}: referred_id={ref.get('referred_id')}, username={ref.get('referred_username')}")

        return result
    except Exception as e:
        logger.error(f"Error getting referral list for user {user_id}: {e}")
        return []
